{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/dist/**/*", "!**/node_modules/**/*", "!**/.turbo/**/*", "!**/tsconfig*.json", "!**/documents/**/*.ts", "!**/.cache-synpress/**/*", "!**/playwright-report/**/*", "!**/examples/**/*", "!**/.next/**/*", "!**/documentation/**/*"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedImports": "warn", "useExhaustiveDependencies": "warn"}, "style": {"useImportType": "off", "noNonNullAssertion": "off", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "suspicious": {"noExplicitAny": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}, "parser": {"unsafeParameterDecoratorsEnabled": true}}}