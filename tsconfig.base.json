{
  // This tsconfig file contains the shared config for the build (tsconfig.build.json) and type checking (tsconfig.json) config.
  "include": [],
  "compilerOptions": {
    // Incremental builds
    // NOTE: Enabling incremental builds speeds up `tsc`. Keep in mind though that it does not reliably bust the cache when the `tsconfig.json` file changes.
    "incremental": true,

    // Type checking
    "strict": true,
    "noFallthroughCasesInSwitch": true, // Not enabled by default in `strict` mode.
    "noImplicitOverride": true, // Not enabled by default in `strict` mode.
    "noImplicitReturns": true, // Not enabled by default in `strict` mode.
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true, // Not enabled by default in `strict` mode.
    "noUnusedParameters": true, // Not enabled by default in `strict` mode.
    "useDefineForClassFields": true, // Not enabled by default in `strict` mode unless we bump `target` to ES2022.
    "useUnknownInCatchVariables": true,
    "resolveJsonModule": true,
    "allowImportingTsExtensions": true,


    // JavaScript support
    "allowJs": false,
    "checkJs": false,

    // Interop constraints
    "forceConsistentCasingInFileNames": true,
    "verbatimModuleSyntax": true,

    // Language and environment
    "moduleResolution": "node",
    "target": "es2021", // Setting this to `ES2021` enables native support for `Node v16+`: https://github.com/microsoft/TypeScript/wiki/Node-Target-Mapping.
    "lib": [
      "ES2022", // By using ES2022 we get access to the `.cause` property on `Error` instances.
      "DOM" // We are adding `DOM` here to get the `fetch`, etc. types. This should be removed once these types are available via DefinitelyTyped.
    ],

    // Skip type checking for node modules
    "skipLibCheck": true,
    "noErrorTruncation": true
  }
}
