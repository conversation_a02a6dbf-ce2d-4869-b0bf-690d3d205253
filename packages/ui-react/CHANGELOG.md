# @betswirl/ui-react

## 0.1.11

### Patch Changes

- Replace gross/raw multiplier/house edge per "BP" type

- Updated dependencies []:
  - @betswirl/wagmi-provider@0.1.15
  - @betswirl/sdk-core@0.1.15

## 0.1.10

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.14
  - @betswirl/wagmi-provider@0.1.14

## 0.1.9

### Patch Changes

- Make supportedChains parameter required, update provider configs, update docs

## 0.1.8

### Patch Changes

- Update documentation and fix configuration files

## 0.1.7

### Patch Changes

- Add development documentation and Wheel game support
