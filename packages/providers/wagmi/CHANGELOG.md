# @betswirl/wagmi-provider

## 0.1.15

### Patch Changes

- Replace gross/raw multiplier/house edge per "BP" type

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.15

## 0.1.14

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.14

## 0.1.13

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.13

## 0.1.12

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.12

## 0.1.11

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.11

## 0.1.10

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.10

## 0.1.9

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.9

## 0.1.8

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.8

## 0.1.7

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.7

## 0.1.6

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.6

## 0.1.5

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.5

## 0.1.4

### Patch Changes

- Add refreshLeaderboardsWithBets

- [`c3143f9`](https://github.com/BetSwirl/sdk/commit/c3143f99ccd603a860ae33148c619e30521d2b58) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getClaimableAmount & claimLeaderboardRewards

- Updated dependencies [[`66d6845`](https://github.com/BetSwirl/sdk/commit/66d6845ecce25ad4cbcfb2c4d504b041e09ec2eb), [`c3143f9`](https://github.com/BetSwirl/sdk/commit/c3143f99ccd603a860ae33148c619e30521d2b58)]:
  - @betswirl/sdk-core@0.1.4

## 0.1.4-beta.1

### Patch Changes

- Add refreshLeaderboardsWithBets

- [`c3143f9`](https://github.com/BetSwirl/sdk/commit/c3143f99ccd603a860ae33148c619e30521d2b58) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getClaimableAmount & claimLeaderboardRewards

- Updated dependencies [[`c3143f9`](https://github.com/BetSwirl/sdk/commit/c3143f99ccd603a860ae33148c619e30521d2b58)]:
  - @betswirl/sdk-core@0.1.4-beta.1

## 0.1.4-beta.0

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.4-beta.0

## 0.1.3

### Patch Changes

- [`76197b5`](https://github.com/BetSwirl/sdk/commit/76197b5e5b89c82730d48dd2c79681d2b3d86703) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add Plinko game

- Updated dependencies [[`76197b5`](https://github.com/BetSwirl/sdk/commit/76197b5e5b89c82730d48dd2c79681d2b3d86703), [`4056f6a`](https://github.com/BetSwirl/sdk/commit/4056f6a242ac5d438cbc90d9a686e06cc245d39e), [`c33ac72`](https://github.com/BetSwirl/sdk/commit/c33ac72cfdbe252b601874f431c02f6e15e4094e), [`addc6f8`](https://github.com/BetSwirl/sdk/commit/addc6f8fa63da599895e106e76a7de5271e2d841)]:
  - @betswirl/sdk-core@0.1.3

## 0.1.3-beta.3

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.3-beta.3

## 0.1.3-beta.2

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.3-beta.2

## 0.1.3-beta.1

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.3-beta.1

## 0.1.3-beta.0

### Patch Changes

- Add Plinko game

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.3-beta.0

## 0.1.2

### Patch Changes

- [`a8f5653`](https://github.com/BetSwirl/sdk/commit/a8f565323c65253a25af73f5a39cf45268e83807) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getWaitRollEventData

- [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add place freebet

- [`d040470`](https://github.com/BetSwirl/sdk/commit/d0404703137aa47ad934f1e1b32901ddc0c889ff) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getPlaceBetEventData

- [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add fetch campaign & freebets from clients

- Updated dependencies [[`a8f5653`](https://github.com/BetSwirl/sdk/commit/a8f565323c65253a25af73f5a39cf45268e83807), [`92fb0c2`](https://github.com/BetSwirl/sdk/commit/92fb0c2537deeae3502ceb507feed0bb8471730b), [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929), [`d040470`](https://github.com/BetSwirl/sdk/commit/d0404703137aa47ad934f1e1b32901ddc0c889ff), [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929)]:
  - @betswirl/sdk-core@0.1.2

## 0.1.2-beta.3

### Patch Changes

- Add getPlaceBetEventData

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.2-beta.3

## 0.1.2-beta.2

### Patch Changes

- Add getWaitRollEventData

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.2-beta.2

## 0.1.2-beta.1

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.2-beta.1

## 0.1.2-beta.0

### Patch Changes

- Add place freebet

- Add fetch campaign & freebets from clients

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.2-beta.0

## 0.1.1

### Patch Changes

- Updated dependencies [[`41a7eed`](https://github.com/BetSwirl/sdk/commit/41a7eed274fccaa774102184f8e973db7b2e6fff)]:
  - @betswirl/sdk-core@0.1.1

## 0.1.0

### Minor Changes

- Add Wheel game with weighted gamr logic

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.1.0

## 0.0.9

### Patch Changes

- Add Keno Game

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.9

## 0.0.8

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.8

## 0.0.7

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.7

## 0.0.6

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.6

## 0.0.5

### Patch Changes

- Make options in wait functions optional

- Improve waitRoll return type

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.5

## 0.0.4

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.4

## 0.0.3

### Patch Changes

- Rename Client

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.3

## 0.0.2

### Patch Changes

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.2

## 0.0.1

### Patch Changes

- Initial release

- Updated dependencies []:
  - @betswirl/sdk-core@0.0.1
