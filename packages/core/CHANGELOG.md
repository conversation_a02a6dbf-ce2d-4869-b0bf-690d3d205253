# @betswirl/sdk-core

## 0.1.15

### Patch Changes

- Replace gross/raw multiplier/house edge per "BP" type

- Add isSingleRolledWin to entities

## 0.1.14

### Patch Changes

- Add optimism chain

## 0.1.13

### Patch Changes

- Add freebet code campaings fetches

## 0.1.12

### Patch Changes

- Fix fetchLeaderboard query options$

## 0.1.11

### Patch Changes

- Export format functions from campaigns and leaderboards

## 0.1.10

### Patch Changes

- Add credentials to fetchFreebetCampaign

## 0.1.9

### Patch Changes

- Fix Keno table typo

## 0.1.8

### Patch Changes

- Update CasinoBetParams

## 0.1.7

### Patch Changes

- Add USD sources to Leaderboard

## 0.1.6

### Patch Changes

- Add formattedNetMultiplier to getPayoutDetails

## 0.1.5

### Patch Changes

- Add getPayoutDetails

## 0.1.4

### Patch Changes

- Add refreshLeaderboardsWithBets

- [`66d6845`](https://github.com/BetSwirl/sdk/commit/66d6845ecce25ad4cbcfb2c4d504b041e09ec2eb) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add fetchLeaderboard, fetchLeaderboards, fetchAffilliateLeaderboard & fetchAffiliateLeaderboards

- [`c3143f9`](https://github.com/BetSwirl/sdk/commit/c3143f99ccd603a860ae33148c619e30521d2b58) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getClaimableAmount & claimLeaderboardRewards

## 0.1.4-beta.1

### Patch Changes

- Add refreshLeaderboardsWithBets

- [`c3143f9`](https://github.com/BetSwirl/sdk/commit/c3143f99ccd603a860ae33148c619e30521d2b58) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getClaimableAmount & claimLeaderboardRewards

## 0.1.4-beta.0

### Patch Changes

- Add fetchLeaderboard, fetchLeaderboards, fetchAffilliateLeaderboard & fetchAffiliateLeaderboards

## 0.1.3

### Patch Changes

- [`76197b5`](https://github.com/BetSwirl/sdk/commit/76197b5e5b89c82730d48dd2c79681d2b3d86703) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add Plinko game

- [`4056f6a`](https://github.com/BetSwirl/sdk/commit/4056f6a242ac5d438cbc90d9a686e06cc245d39e) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Force Deploy

- [`c33ac72`](https://github.com/BetSwirl/sdk/commit/c33ac72cfdbe252b601874f431c02f6e15e4094e) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Update getUniqueOutputs & getSortedPlinkoOutputs

- [`addc6f8`](https://github.com/BetSwirl/sdk/commit/addc6f8fa63da599895e106e76a7de5271e2d841) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Fix fetchTokens & fetchBets default caching

## 0.1.3-beta.3

### Patch Changes

- Fix fetchTokens & fetchBets default caching

## 0.1.3-beta.2

### Patch Changes

- Force Deploy

## 0.1.3-beta.1

### Patch Changes

- Update getUniqueOutputs & getSortedPlinkoOutputs

## 0.1.3-beta.0

### Patch Changes

- Add Plinko game

## 0.1.2

### Patch Changes

- [`a8f5653`](https://github.com/BetSwirl/sdk/commit/a8f565323c65253a25af73f5a39cf45268e83807) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getWaitRollEventData

- [`92fb0c2`](https://github.com/BetSwirl/sdk/commit/92fb0c2537deeae3502ceb507feed0bb8471730b) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Update fetchFreebetCampaigns return type

- [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add place freebet

- [`d040470`](https://github.com/BetSwirl/sdk/commit/d0404703137aa47ad934f1e1b32901ddc0c889ff) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add getPlaceBetEventData

- [`0f1dc7d`](https://github.com/BetSwirl/sdk/commit/0f1dc7dfdf4434c84c531c6999b5f8b2d635a929) Thanks [@Kinco-dev](https://github.com/Kinco-dev)! - Add fetch campaign & freebets from clients

## 0.1.2-beta.3

### Patch Changes

- Add getPlaceBetEventData

## 0.1.2-beta.2

### Patch Changes

- Add getWaitRollEventData

## 0.1.2-beta.1

### Patch Changes

- Update fetchFreebetCampaigns return type

## 0.1.2-beta.0

### Patch Changes

- Add place freebet

- Add fetch campaign & freebets from clients

## 0.1.1

### Patch Changes

- Improve getPlaceBetFunctionData params typing

- Add freebet & campaign fetching

## 0.1.0

### Minor Changes

- Add Wheel game with weighted gamr logic

## 0.0.9

### Patch Changes

- Add Keno Game

## 0.0.8

### Patch Changes

- Update encodeInput from CoinToss

## 0.0.7

### Patch Changes

- Update formatCasinoRolledBet

## 0.0.6

### Patch Changes

- Update subgraph urls

## 0.0.5

### Patch Changes

- Add getBetSwirlBetUrl util

- Make options in wait functions optional

- Improve waitRoll return type

- Add getCasinoGamePaused

## 0.0.4

### Patch Changes

- Make walletClient optional from ViemClient and ViemWallet

## 0.0.3

### Patch Changes

- Add Viem client and wallet

## 0.0.2

### Patch Changes

- Update parseRawBetRequirements to include isAllowed

## 0.0.1

### Patch Changes

- Initial release
