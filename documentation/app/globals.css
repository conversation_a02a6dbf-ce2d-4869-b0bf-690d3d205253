@import "tailwindcss";
@import "nextra-theme-docs/style.css";
:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Style pour les blocs de code Nextra */
.nextra-code-block,
.nextra-code-block *,
.nextra-code-block pre,
.nextra-code-block > div,
.nextra-code-block > div * {
  background-color: #e6f3ff !important;
}

/* Style pour les blocs de code inline */
.nextra-code,
.nextra-code * {
  background-color: #e6f3ff !important;
}

/* Style spécifique pour les bordures */
[data-theme="light"] .nextra-code-block,
[data-theme="light"] .nextra-code-block *,
[data-theme="light"] .nextra-code {
  background-color: #e6f3ff !important;
}


