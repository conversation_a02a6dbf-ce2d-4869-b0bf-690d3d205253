import { Cards } from 'nextra/components'
import { Callout } from 'nextra/components'
import { Tabs, Tab } from 'nextra/components'
import { Steps } from 'nextra/components'
import { Users, Coins, Gamepad2, Sparkles, Zap, Shield } from 'lucide-react'
import Image from 'next/image'

# ⚔️ Player vs Player Games

BetSwirl's PvP (Player vs Player) games bring the thrill of competitive gaming to the blockchain. These games allow players to challenge each other directly, creating exciting head-to-head battles where only luck determines the winner.

Unlike traditional casino games where players compete against the house, PvP games create a dynamic environment where players can host games, set their own rules, and compete directly against other players.

<Image 
  src="/img/headers/casino.png" 
  alt="BetSwirl Banner" 
  width={1200} 
  height={300} 
  className="w-full rounded-lg mb-6"
  priority
/>

<Callout type="info">
  PvP games on BetSwirl are fully decentralized, allowing players to host their own games and set custom parameters while maintaining provably fair outcomes.
</Callout>

## How PvP Games Work

PvP games on BetSwirl follow a unique model where players can become hosts and create their own gambling sessions. This creates a dynamic ecosystem where anyone can participate as either a host or a player.

<Steps>
### Step 1: Game Creation
A host creates a game by setting parameters like buy-in amount, number of players, deadline, and game rules. They can choose to make the game public or private.

### Step 2: Player Registration
Players join the game by paying the buy-in amount. Once all seats are filled or the deadline is reached, the game is ready to begin.

### Step 3: Game Resolution
The game is resolved using Chainlink VRF for provably fair random outcomes, ensuring complete transparency and trust.

### Step 4: Prize Distribution
Winners receive their share of the prize pool, while the host benefits from a portion of the house edge, creating incentives for game hosting.
</Steps>

## Available PvP Games

{(() => {
  const pvpGames = [
    {
      emoji: "🔫",
      title: "Russian Roulette",
      description: "Can you profit by gambling? Sure you can if you host big Russian Roulette. Host creates a Russian Roulette game by setting the number of open seats, the buy-in, a deadline date and the winning percentage. They can also choose to make the game public or private. Once all seats are filled or the deadline arrives, the game is ready for resolution. When the trigger is pulled, winners are determined, sharing the total prize pool minus the house edge. Importantly, the host benefits from this house edge, receiving 50% of it, which adds an element of profitability for them.",
      image: "/img/games/russian-roulette.png"
    },
    {
      emoji: "🪙",
      title: "Coin Flip Battle",
      description: "Host creates a Coin Flip battle by setting the game variables, including the coin side and the buy-in. Then the battle is established on-chain and can only be canceled by the host if no opponents are registered. The host can also choose to make the game private, allowing only specific opponents to join.",
      image: "/img/games/coin-flip-battle.png"
    }
  ];

  return pvpGames.map((game, index) => (
    <div key={index}>
      <h3 className="text-2xl font-bold p-2">{game.emoji} {game.title}</h3>
      <div className="flex flex-col w-full bg-card text-card-foreground rounded-lg">
        <div className="text-base leading-relaxed p-2 mb-4">
          {game.description}
        </div>
        
        <div className="flex justify-center mb-8">
          <Image 
            src={game.image} 
            alt={`${game.title} Game`} 
            width={800} 
            height={500} 
            className="rounded-lg shadow-lg"
          />
        </div>
      </div>
    </div>
  ));
})()}

## 🏦 Host Benefits

In PvP games, hosts play a crucial role in creating gaming opportunities and are rewarded for their participation. Hosts receive a portion of the house edge, typically 50%, providing them with a profitable incentive to create and manage games.

<Callout type="warning">
  Hosting PvP games can be profitable, but requires careful management of game parameters and player engagement.
</Callout>

## 🔗 Smart Contracts

For detailed information about the smart contracts used by each PvP game, please refer to the [Smart Contracts](/protocol-hub/protocol/contracts#%EF%B8%8F-pvp-game-contracts) section.
