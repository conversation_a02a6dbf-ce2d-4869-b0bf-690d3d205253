import { Cards, Callout, Steps } from 'nextra/components'
import Image from 'next/image'

# BetSwirl Affiliate Program

As a BetSwirl affiliate, you build frontend applications on top of our casino protocol and earn **30% of house edge revenue** from all bets placed through your interface.

<Image 
  src="/img/headers/affiliates.png" 
  alt="BetSwirl Banner" 
  width={1200} 
  height={300} 
  className="w-full rounded-lg mb-6"
  priority
/>

## What is an Affiliate?

Affiliates are developers and entrepreneurs who create user-friendly interfaces for BetSwirl's casino games. You don't need to be a developer to become an affiliate - you can fork demos and deploy them with our help.

### Key Benefits:
- **30% of house edge revenue** from all bets
- **Permissionless integration** - build your frontend without approval
- **Complete control** over your user experience and branding

## Getting Started

<Steps>
### Build Your Frontend

Start by creating your frontend application using our SDKs. Check out our [Developer Hub](/developer-hub/introduction) for comprehensive documentation and integration guides.

The integration is **completely permissionless** - you can start building immediately without any approval from BetSwirl.

### Access Your Dashboard

Once your frontend is live, you can access the [Affiliate Dashboard](https://www.betswirl.com/affiliate/freebets) where you can manage all aspects of your partnership.

### Unlock Premium Features (optional)

Once you feel ready, tell us more about your app and unlock freebet and leaderboard features.

### Claim your earnings

Claim what you earned from house edge revenue. [Claim your earnings](https://www.betswirl.com/base/affiliate/revenues)
</Steps>

## Dashboard Features

import { Tabs } from 'nextra/components'

<Tabs items={['🎁 Freebets', '🏆 Leaderboards', '💰 Revenue', '⚙️ House Edge', '👥 Analytics', '👨‍💼 Team']}>
  <Tabs.Tab>
    ### 🎁 Freebets Management
    **Access**: [Freebets Dashboard](https://www.betswirl.com/affiliate/freebets) *(Requires BetSwirl authorization)*
    
    Create and distribute freebets to attract new users to your platform.
    
    <Image 
      src="/img/dashboard/freebets.png" 
      alt="Freebets Management Dashboard" 
      width={0}
      height={0}
      sizes="100vw"
      className="rounded-lg border mt-4 w-full h-auto"
    />
  </Tabs.Tab>
  
  <Tabs.Tab>
    ### 🏆 Leaderboards
    **Access**: [Leaderboards Dashboard](https://www.betswirl.com/base/affiliate/leaderboards) *(Requires BetSwirl authorization)*
    
    Set up competitive leaderboards to engage your users and create additional revenue streams through tournament-style gameplay.
    
    <Image 
      src="/img/dashboard/leaderboards.png" 
      alt="Leaderboards Dashboard" 
      width={0}
      height={0}
      sizes="100vw"
      className="rounded-lg border mt-4 w-full h-auto"
    />
  </Tabs.Tab>
  
  <Tabs.Tab>
    ### 💰 Revenue Management
    **Access**: [Revenues Dashboard](https://www.betswirl.com/base/affiliate/revenues)
    
    Claim your 30% share of house edge revenue from all bets placed through your frontend. Track your earnings in real-time.
    
    <Image 
      src="/img/dashboard/revenue.png" 
      alt="Revenue Management Dashboard" 
      width={0}
      height={0}
      sizes="100vw"
      className="rounded-lg border mt-4 w-full h-auto"
    />
  </Tabs.Tab>
  
  <Tabs.Tab>
    ### ⚙️ House Edge Configuration
    **Access**: [House Edge Dashboard](https://www.betswirl.com/base/affiliate/house-edge)
    
    Customize house edge settings for different games to optimize your revenue and user experience.
    
    <Image 
      src="/img/dashboard/house-edge.png" 
      alt="House Edge Configuration Dashboard" 
      width={0}
      height={0}
      sizes="100vw"
      className="rounded-lg border mt-4 w-full h-auto"
    />
  </Tabs.Tab>
  
  <Tabs.Tab>
    ### 👥 User Analytics
    **Access**: [Users Dashboard](https://www.betswirl.com/base/affiliate/affiliated-users)
    
    Monitor your user base, track engagement, and analyze betting patterns to optimize your platform.
    
    <Image 
      src="/img/dashboard/analytics.png" 
      alt="User Analytics Dashboard" 
      width={0}
      height={0}
      sizes="100vw"
      className="rounded-lg border mt-4 w-full h-auto"
    />
  </Tabs.Tab>
  
  <Tabs.Tab>
    ### 👨‍💼 Team Management
    **Access**: [Whitelist Management](https://www.betswirl.com/affiliate/whitelist)
    
    Manage your team members, set permissions, and control access to your affiliate features.
    
    <Image 
      src="/img/dashboard/team.png" 
      alt="Team Management Dashboard" 
      width={0}
      height={0}
      sizes="100vw"
      className="rounded-lg border mt-4 w-full h-auto"
    />
  </Tabs.Tab>
</Tabs>

<Callout type="info">
  **Permissionless vs Authorized Features**: While building your frontend is completely permissionless, the **Freebets** and **Leaderboards** features require explicit authorization from BetSwirl. Contact us to request access to these premium features.
</Callout>


## Need Support?

Join our community channels for technical assistance and partnership guidance:

- **Telegram**: [BetSwirl Affiliates](https://t.me/betswirl_affiliates)
- **Discord**: [Join our Discord](https://discord.gg/xBTWTtrwbs)

Our team is ready to help you succeed as a BetSwirl affiliate! 