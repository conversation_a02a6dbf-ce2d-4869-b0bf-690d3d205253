import { Cards } from 'nextra/components'
import { Callout } from 'nextra/components'
import { Tabs, Tab } from 'nextra/components'
import { Steps } from 'nextra/components'
import { Trophy, Shield, Globe, Zap, Gamepad2, Sparkles, Users, Target, Link, Database, Lock } from 'lucide-react'
import Image from 'next/image'

# 🏆 Azuro Protocol

Azuro is a **decentralized sports betting protocol** that powers BetSwirl's sports betting platform. It provides the underlying infrastructure for transparent, fair, and censorship-resistant sports betting on the blockchain.

<Image 
  src="/img/headers/azuro.png" 
  alt="Azuro Protocol Banner" 
  width={1200} 
  height={300} 
  className="w-full rounded-lg mb-6"
  priority
/>

<Callout type="info">
  Azuro is the backbone of decentralized sports betting, providing the smart contracts, liquidity pools, and oracle integrations that make on-chain betting possible.
</Callout>

## 🔗 About Azuro

[Azuro](https://azuro.org/) is a leading decentralized sports betting protocol that enables permissionless, transparent, and fair sports betting on the blockchain. It serves as the infrastructure layer that powers various frontend applications, including BetSwirl's sports betting platform.

### **Core Features:**

- **Permissionless Access** - Anyone can build on top of the protocol
- **Transparent Odds** - Real-time, on-chain odds that cannot be manipulated
- **Liquidity Pools** - Deep liquidity for various betting markets
- **Oracle Integration** - Reliable result verification through decentralized oracles
- **Cross-Chain Support** - Available on multiple blockchains

## 🏗️ How Azuro Works

Azuro operates as a decentralized protocol that connects bettors, liquidity providers, and oracle networks through smart contracts.

<Steps>
### Step 1: Market Creation
Bookmaker providers create betting markets for various sports events with initial odds and liquidity.

### Step 2: Bet Placement
Users sign messages to place bets through frontend applications (like BetSwirl). The actual on-chain transaction is handled by Azuro's servers.

### Step 3: Oracle Resolution
Decentralized oracles provide verified results for sports events, ensuring fair and tamper-proof outcomes.

### Step 4: Automatic Settlement
Smart contracts automatically settle bets and distribute winnings to winners based on oracle results.

### Step 5: Claim Winnings
Users claim their winnings by executing an on-chain transaction, which transfers funds directly to their wallet.
</Steps>

## 🌐 Supported Blockchains

Azuro is available on multiple blockchains to ensure maximum accessibility and reduce transaction costs:

| Blockchain | Type | Description |
|------------|------|-------------|
| **Polygon** | Layer 2 | Low fees, fast transactions |
| **Gnosis Chain** | Layer 2 | Ethereum L2 solution |
| **Base** | Layer 2 | Coinbase's L2 network |

## 🎯 Key Components

### **Liquidity Pools**
Liquidity providers supply funds to betting markets, enabling users to place bets. They earn fees from betting activity and can withdraw their liquidity at any time.

### **Frontend Applications**
Applications like BetSwirl provide user-friendly interfaces for interacting with the Azuro protocol. They handle:
- Displaying available betting markets
- Facilitating wallet connections and betting transactions
- Showing betting history and results



## 🏦 Benefits of Azuro

### **For Bettors:**
- **No Intermediaries** - Bet directly with other users through smart contracts
- **Transparent Odds** - Real-time, on-chain odds that cannot be manipulated
- **Instant Payouts** - Automatic settlement and distribution of winnings
- **Global Access** - No geographical restrictions or KYC requirements

### **For Developers:**
- **Permissionless Access** - Anyone can build applications on top of Azuro
- **Comprehensive APIs** - Easy integration with existing applications
- **Liquidity Sharing** - Access to deep liquidity pools across multiple markets

### **For Liquidity Providers:**
- **Fee Earnings** - Earn fees from betting activity
- **Flexible Liquidity** - Add or remove liquidity at any time
- **Risk Management** - Diversify across multiple betting markets
- **Transparent Returns** - All earnings and losses are visible on-chain

## 🔗 Learn More

To learn more about Azuro and explore their ecosystem, visit their official website:

<div className="flex justify-center mb-8">
  <a 
    href="https://azuro.org/" 
    target="_blank" 
    rel="noopener noreferrer"
    className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
  >
    Visit Azuro.org
  </a>
</div>

---

**Azuro is the foundation of decentralized sports betting, enabling transparent and fair betting experiences across the blockchain ecosystem.**