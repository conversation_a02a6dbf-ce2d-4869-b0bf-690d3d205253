import { Cards } from 'nextra/components'
import { Callout } from 'nextra/components'
import { Tabs, Tab } from 'nextra/components'
import { Shield, Link, Database, FileText, Globe, Zap } from 'lucide-react'
import Image from 'next/image'

# 🔗 Smart Contracts

BetSwirl's smart contracts are the backbone of our decentralized gaming platform. All contracts are audited by Paladin and leverage Chainlink's VRF for provably fair gaming across multiple blockchains.
<Callout type="info">
  All BetSwirl contracts are deployed across multiple blockchains for maximum accessibility and cross-chain compatibility.
</Callout>

## 🎯 BETS Token Contracts

| Contract | Networks |
|----------|----------|
| **BETS Token** | [Ethereum](https://etherscan.io/token/******************************************), [Polygon](https://polygonscan.com/token/******************************************), [BNB Chain](https://bscscan.com/token/******************************************), [Avalanche](https://snowscan.xyz/token/******************************************), [Arbitrum One](https://arbiscan.io/token/******************************************), [Base](https://basescan.org/token/******************************************), [Unichain](https://uniscan.xyz/token/******************************************) |
| **Staking (inactive)** | [Ethereum](https://etherscan.io/address/******************************************), [Polygon](https://polygonscan.com/address/******************************************), [BNB Chain](https://bscscan.com/address/******************************************), [Avalanche](https://snowscan.xyz/address/******************************************), [Arbitrum One](https://arbiscan.io/address/******************************************), [Base](https://basescan.org/address/******************************************) |

## 🎰 Casino Game Contracts

| Game | Networks |
|------|----------|
| **Bank** | [Polygon](https://polygonscan.com/address/******************************************), [BNB Chain](https://bscscan.com/address/******************************************), [Avalanche](https://snowscan.xyz/address/******************************************), [Arbitrum One](https://arbiscan.io/address/******************************************), [Base](https://basescan.org/address/******************************************) |
| **Coin Toss** | [Polygon](https://polygonscan.com/address/0xC3Dff2489F8241729B824e23eD01F986fcDf8ec3), [BNB Chain](https://bscscan.com/address/0xC3Dff2489F8241729B824e23eD01F986fcDf8ec3), [Avalanche](https://snowscan.xyz/address/0xC3Dff2489F8241729B824e23eD01F986fcDf8ec3), [Arbitrum One](https://arbiscan.io/address/0xC3Dff2489F8241729B824e23eD01F986fcDf8ec3), [Base](https://basescan.org/address/0xC3Dff2489F8241729B824e23eD01F986fcDf8ec3) |
| **Dice** | [Polygon](https://polygonscan.com/address/0xAa4D2931a9fE14c3dec8AC3f12923Cbb535C0e5f), [BNB Chain](https://bscscan.com/address/0xAa4D2931a9fE14c3dec8AC3f12923Cbb535C0e5f), [Avalanche](https://snowscan.xyz/address/0xAa4D2931a9fE14c3dec8AC3f12923Cbb535C0e5f), [Arbitrum One](https://arbiscan.io/address/0xAa4D2931a9fE14c3dec8AC3f12923Cbb535C0e5f), [Base](https://basescan.org/address/0xAa4D2931a9fE14c3dec8AC3f12923Cbb535C0e5f) |
| **Roulette** | [Polygon](https://polygonscan.com/address/0x6678e3B4AB2a8C8Cdd068F132C21293CcBda33cb), [BNB Chain](https://bscscan.com/address/0x6678e3B4AB2a8C8Cdd068F132C21293CcBda33cb), [Avalanche](https://snowscan.xyz/address/0x6678e3B4AB2a8C8Cdd068F132C21293CcBda33cb), [Arbitrum One](https://arbiscan.io/address/0x6678e3B4AB2a8C8Cdd068F132C21293CcBda33cb), [Base](https://basescan.org/address/0x6678e3B4AB2a8C8Cdd068F132C21293CcBda33cb) |
| **Keno** | [Polygon](https://polygonscan.com/address/0xc3428E4FEb5C770Db51DCb9B1C08223B10994a89), [BNB Chain](https://bscscan.com/address/0xc3428E4FEb5C770Db51DCb9B1C08223B10994a89), [Avalanche](https://snowscan.xyz/address/0xc3428E4FEb5C770Db51DCb9B1C08223B10994a89), [Arbitrum One](https://arbiscan.io/address/0xc3428E4FEb5C770Db51DCb9B1C08223B10994a89), [Base](https://basescan.org/address/0xc3428E4FEb5C770Db51DCb9B1C08223B10994a89) |
| **Wheel & Plinko** | [Polygon](https://polygonscan.com/address/0xdec2A4f75c5fAE4a09c83975681CE1Dd1dff764b), [BNB Chain](https://bscscan.com/address/0xdec2A4f75c5fAE4a09c83975681CE1Dd1dff764b), [Avalanche](https://snowscan.xyz/address/0xdec2A4f75c5fAE4a09c83975681CE1Dd1dff764b), [Arbitrum One](https://arbiscan.io/address/0xdec2A4f75c5fAE4a09c83975681CE1Dd1dff764b), [Base](https://basescan.org/address/0xdec2A4f75c5fAE4a09c83975681CE1Dd1dff764b) |

## ⚔️ PvP Game Contracts

| Game | Networks |
|------|----------|
| **Game Store** | [Polygon](https://polygonscan.com/address/0xfCDda7A4437c2D69e33592582D3793626710070F), [BNB Chain](https://bscscan.com/address/0xfCDda7A4437c2D69e33592582D3793626710070F), [Avalanche](https://snowscan.xyz/address/0xfCDda7A4437c2D69e33592582D3793626710070F), [Arbitrum One](https://arbiscan.io/address/0xfCDda7A4437c2D69e33592582D3793626710070F), [Base](https://basescan.org/address/0xfCDda7A4437c2D69e33592582D3793626710070F) |
| **Coin Flip Battle** | [Polygon](https://polygonscan.com/address/0xA79FD774185b5F70e92dcC8F7437E106D648E17F), [BNB Chain](https://bscscan.com/address/0xA79FD774185b5F70e92dcC8F7437E106D648E17F), [Avalanche](https://snowscan.xyz/address/0xA79FD774185b5F70e92dcC8F7437E106D648E17F), [Arbitrum One](https://arbiscan.io/address/0xA79FD774185b5F70e92dcC8F7437E106D648E17F), [Base](https://basescan.org/address/0xA79FD774185b5F70e92dcC8F7437E106D648E17F) |
| **Russian Roulette** | [Polygon](https://polygonscan.com/address/******************************************), [BNB Chain](https://bscscan.com/address/******************************************), [Avalanche](https://snowscan.xyz/address/******************************************), [Arbitrum One](https://arbiscan.io/address/******************************************), [Base](https://basescan.org/address/******************************************) |

## 🏛️ Internal Wallets

| Purpose | Networks |
|---------|----------|
| **Treasury (multisig)** | [Ethereum](https://etherscan.io/address/******************************************), [Polygon](https://polygonscan.com/address/******************************************), [BNB Chain](https://bscscan.com/address/******************************************), [Avalanche](https://snowscan.xyz/address/******************************************), [Arbitrum One](https://arbiscan.io/address/******************************************), [Gnosis](https://gnosisscan.io/address/******************************************), [Optimism](https://optimistic.etherscan.io/address/******************************************), [Base](https://basescan.org/address/******************************************), [Unichain](https://uniscan.xyz/address/******************************************) |
| **Dividends (multisig)** | [Ethereum](https://etherscan.io/address/******************************************), [Polygon](https://polygonscan.com/address/******************************************), [BNB Chain](https://bscscan.com/address/******************************************), [Avalanche](https://snowscan.xyz/address/******************************************), [Arbitrum One](https://arbiscan.io/address/******************************************), [Base](https://basescan.org/address/******************************************) |
| **Core Team** | [Polygon](https://polygonscan.com/address/******************************************), [BNB Chain](https://bscscan.com/address/******************************************), [Avalanche](https://snowscan.xyz/address/******************************************), [Arbitrum One](https://arbiscan.io/address/******************************************), [Base](https://basescan.org/address/******************************************) |


## 🔍 Security & Audits

<Cards>
  <Cards.Card
    title="Paladin Audit Report"
    href="https://paladinsec.co/projects/betswirl"
    icon={<Image src="/img/paladin.svg" alt="Paladin" width={24} height={24} />}
    target="_blank"
    rel="noopener noreferrer"
  />
</Cards>

## 🔗 Chainlink Integration

At BetSwirl, we leverage Chainlink's powerful decentralized oracle network to enhance the integrity and transparency of our gaming platform.

### VRF Subscriptions

BetSwirl uses Chainlink VRF for provably fair random number generation across all supported networks:

| Network | VRF Subscription |
|---------|------------------|
| **Arbitrum One** | [View Subscription](https://vrf.chain.link/arbitrum#/side-drawer/subscription/arbitrum/26510450093329368004237878416199508562205945994063563127634409219041040756566) |
| **Avalanche** | [View Subscription](https://vrf.chain.link/avalanche#/side-drawer/subscription/avalanche/87060360337790157170135218934045586905659360396458026876115663885747477615369) |
| **Polygon** | [View Subscription](https://vrf.chain.link/polygon#/side-drawer/subscription/polygon/79838174144652451313763716789896498714546673967850869672884710820136443316484) |
| **BNB Chain** | [View Subscription](https://vrf.chain.link/bsc#/side-drawer/subscription/bsc/64778934296371996396369853439912130360272408155611603044356319460986888838555) |
| **Base** | [View Subscription](https://vrf.chain.link/base#/side-drawer/subscription/base/93271074453882857048982544254580571499844752099945152824703671195862788259559) |


### VRF for Provably Fair Gaming

We utilize Chainlink's Verifiable Random Function (VRF) to generate random numbers for our casino games. This ensures that every outcome is **provably fair** and **tamper-proof**, providing players with confidence in the integrity of their gaming experience.

### Cross-Chain Interoperability Protocol (CCIP)

We utilize CCIP to facilitate seamless transactions across different blockchain networks. This bridge allows our users to move assets effortlessly between chains, enhancing the flexibility and scalability of our platform.

### Commitment to Chainlink

For all these reasons, we consider ourselves a project that works very closely with Chainlink. We are dedicated to continuously investing in $LINK as a project, recognizing its vital role in the future of decentralized finance and gaming.

## 📊 The Graph Integration

At BetSwirl, we utilize The Graph to enhance our platform's data accessibility and performance.

### Subgraphs

BetSwirl maintains subgraphs across multiple blockchains for efficient data indexing and querying:

| Network | Subgraph Explorer |
|---------|-------------------|
| **Base** | [View Subgraph](https://thegraph.com/explorer/subgraphs/6rt22DL9aaAjJHDUZ25sSsPuvuKxp1Tnf8LBXhL8WdZi?view=Query&chain=arbitrum-one) |
| **Polygon** | [View Subgraph](https://thegraph.com/explorer/subgraphs/FL3ePDCBbShPvfRJTaSCNnehiqxsPHzpLud6CpbHoeKW?view=Query&chain=arbitrum-one) |
| **Avalanche** | [View Subgraph](https://thegraph.com/explorer/subgraphs/4nQJ4T5TXvTxgECqQ6ox6Nwf57d5BNt6SCn7CzzxjDZN?view=Query&chain=arbitrum-one) |
| **BNB Chain** | [View Subgraph](https://thegraph.com/explorer/subgraphs/69xMkatN58qWXZS7FXqiVQmvkHhNrq3thTfdB6t85Wvk?view=Query&chain=arbitrum-one) |
| **Arbitrum** | [View Subgraph](https://thegraph.com/explorer/subgraphs/AsPBS4ymrjoR61r1x2avNJJtMPvzZ3quMHxvQTgDJbU?view=Query&chain=arbitrum-one) |

### Efficient Data Querying

By integrating with The Graph, BetSwirl can efficiently query blockchain data related to games, bets, and user activities. This capability allows us to provide real-time updates and insights.

### Enhanced User Experience

The Graph enables us to create a more responsive and interactive user experience. By indexing data from various blockchain networks, we can deliver seamless navigation and quick access to game statistics and betting history.

### Support for Multiple Blockchains

Our integration with The Graph supports multiple EVM-compatible blockchains, including Polygon, BNB Smart Chain, Avalanche, and Arbitrum. This cross-chain capability ensures that users can access data from their preferred blockchain without any friction.

### Commitment to Decentralization

By leveraging The Graph, we reinforce our commitment to decentralization and transparency. The protocol allows us to maintain a trustless environment where users can verify the data and outcomes of their interactions on the platform.

---

**All contracts are open source and audited. Transparency and security are our top priorities.**