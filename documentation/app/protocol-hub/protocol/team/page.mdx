import { Cards } from 'nextra/components'
import { Callout } from 'nextra/components'
import { Tabs, Tab } from 'nextra/components'
import { <PERSON>, Code, Palette, Brain, Zap, Globe, Shield, Sparkles } from 'lucide-react'
import Image from 'next/image'
import { TeamMember } from '../../../_components/TeamMember'

# 👥 Team

Meet the talented individuals behind BetSwirl who are dedicated to revolutionizing decentralized gaming.


<Callout type="info">
  Our team combines expertise in blockchain, gaming, design, and development to create the ultimate decentralized gaming experience.
</Callout>

## 🏗️ Core Team

### Romuald Hog – Co-founder

<TeamMember 
  name="Romuald Hog" 
  role="Leadership & Strategy" 
  icon={<Shield />} 
  iconColor="text-blue-500" 
  description="<PERSON><PERSON><PERSON><PERSON> is a seasoned leader with over 15 years of experience in engineering and technical innovation. He has previously held key roles in global retail and brings a unique blend of technical and business acumen to BetSwirl, focusing on the platform's growth and strategic direction." 
/>

### <PERSON>o – Co-founder & Product Designer

<TeamMember 
  name="<PERSON><PERSON>" 
  role="Design & UX" 
  icon={<Palette />} 
  iconColor="text-purple-500" 
  description="<PERSON><PERSON> has been a designer since 2011, with a portfolio that includes over 150 clients. His focus on creating clear, user-friendly designs plays a critical role in BetSwirl's user experience. He has been deeply involved in the crypto space for over five years, making major contributions to various projects." 
/>

## 💻 Development Team

### Geisenberg – Front-End Developer

<TeamMember 
  name="Geisenberg" 
  role="Front-End Development" 
  icon={<Code />} 
  iconColor="text-green-500" 
  description="An expert in pixel-perfect integration and aesthetic web development, Geisenberg has been a blockchain enthusiast since 2013. His focus is on creating seamless and engaging front-end experiences for BetSwirl." 
/>

### Kinco – Full-Stack Developer

<TeamMember 
  name="Kinco" 
  role="Full-Stack Development" 
  icon={<Zap />} 
  iconColor="text-yellow-500" 
  description="Kinco is a full-stack medior developer, working on smart contracts as well as Web3.0 front-end integration. He joined the project at the end of 2023 to work on BetSwirl V2." 
/>

### Chainhackers – Full-stack AI-driven Developers

<TeamMember 
  name="Chainhackers" 
  role="AI & Smart Contracts" 
  icon={<Brain />} 
  iconColor="text-cyan-500" 
  description="ChainHackers is a compact team of Ethereum-focused developers who explore emerging technologies hands-on. They specialize in decentralized apps and AI-driven automation, optimizing UX and performance through neural networks and smart contract design." 
/>

## 🎨 Creative Team

### Jay Rillo – Illustrator

<TeamMember 
  name="Jay Rillo" 
  role="Visual Design" 
  icon={<Sparkles />} 
  iconColor="text-pink-500" 
  description="Jay Rillo is an experienced illustrator and storyboard artist. His contributions to BetSwirl involve creating vibrant visual content, enhancing the platform's aesthetic appeal." 
/>

## 📈 Marketing Team

### Gfk – Marketing

<TeamMember 
  name="Gfk" 
  role="Marketing & Growth" 
  icon={<Globe />} 
  iconColor="text-indigo-500" 
  description="Gfk is a poker player into defi, bringing unique insights from both traditional gaming and decentralized finance to BetSwirl's marketing strategy." 
/>

## 🎯 Our Mission

The BetSwirl team's collective experience in blockchain, gaming, design, and development ensures that the platform is built with a focus on **innovation**, **user experience**, and **security**. They are committed to making BetSwirl a leader in the decentralized gaming space.

### Key Strengths

- **15+ years** of combined engineering experience
- **150+ clients** in design portfolio
- **Blockchain expertise** since 2013
- **AI-driven development** capabilities
- **Gaming industry** knowledge
- **DeFi integration** experience

### Commitment to Excellence

Our team is dedicated to:
- **Innovation** in decentralized gaming
- **User-centric design** and experience
- **Security** and transparency
- **Cross-chain compatibility**
- **Community engagement**

---

**Together, we're building the future of decentralized gaming.**