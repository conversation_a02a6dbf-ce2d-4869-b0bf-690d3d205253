{"name": "documentation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "pagefind --site .next/server/app --output-path public/_pagefind"}, "dependencies": {"lucide-react": "^0.525.0", "next": "15.4.5", "nextra": "4.3.0", "nextra-theme-docs": "4.3.0", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@napi-rs/simple-git": "^0.1.21", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "pagefind": "^1.3.0", "tailwindcss": "^4.1.11", "typescript": "^5"}}