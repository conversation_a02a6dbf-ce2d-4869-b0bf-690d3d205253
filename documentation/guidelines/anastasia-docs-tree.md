# BetSwirl Documentation Tree

```
📚 BetSwirl Documentation
│
├── 🏠 Landing Page [✅ EXISTS]
│   └── "What is BetSwirl?" → Protocol Hub / Developer Hub
		 - Target: Developers and potential SDK users  
		 - Goal: Quickly understand if this fits their needs  
		 - Focus: Functionality and benefits
			### 1.1 What is BetSwirl?
			- Decentralized casino on blockchain
			- Ready-to-use smart contracts for casino games
			- SDK for integration into any application
			- Blockchain transparency
			- Chainlink VRF for fair randomness
			
			### 1.2 Who is it for?
			
			**dApp Developers**
			- Add gaming functionality to your app
			- Ready React components
			- Full UI customization
			
			**Website Owners (Affiliates)**
			- Earn ~30% of house edge
			- No need to hold bankroll
			- Turnkey solution
			
			**Mini-app Creators**
			- Telegram bots with games
			- Farcaster frames
			- Mobile PWA
			
			**Liquidity Providers**
			- ~20% of casino profits
			- Portfolio diversification
			- Transparent statistics
			
			### 1.3 Available games?
			- CoinToss - Heads or tails
			- Dice - Dice with adjustable multiplier
			- Roulette - European roulette
			- Keno - Number selection lottery
			- Wheel - Wheel of fortune (coming soon)
			- Plinko - Falling ball (coming soon)
			
			### 1.4 Supported networks?
			- Base - Low fees, fast transactions
			- Polygon - Popular L2 network
			- Avalanche - High throughput
			- Testnets - Base Sepolia, Polygon Amoy, Avalanche Fuji
			
			### 1.5 Protocol economics
			- House Edge: 2.5-4% (configurable)
			- Profit distribution:
			  - ~30% to affiliates
			  - ~20% to bankroll providers
			  - ~50% to protocol and development
			- VRF fees: Small fee for Chainlink randomness
├── 🎯 Protocol Hub [✅ EXISTS - already filled]
		- Target: Investors, partners, journalists  
		- Goal: Impress, sell the idea, show success  
		- Focus: Achievements (Top 2 Farcaster, 750k freebets, $240k IDO)
│   ├── Introduction
│   ├── Games
│   ├── Partners
│   └── Where to Bet
│
└── 👨‍💻 Developer Hub [🔴 EMPTY - needs to be filled]
    │
    ├── 🚀 Getting Started [🔴 PRIORITY 1]
    │   ├── What can I build? (3 use cases)
    │   ├── Prerequisites (wallet, npm, etc)
    │   └── Choose your path:
    │       ├── → Fork MiniApp (fastest)
    │       ├── → Build React App 
    │       └── → Use Core SDK (advanced)
    │
    ├── 🎯 Quick Starts [🔴 PRIORITY 1]
    │   ├── MiniApp in 5 minutes
    │   ├── React App in 10 minutes
    │   └── First Bet in 3 minutes
    │
    ├── 📖 Guides (Mini-guides) [🟡 PRIORITY 2]
    │   ├── Basic Operations
    │   │   ├── How to place a CoinToss bet
    │   │   ├── How to place a Dice bet
    │   │   ├── How to check bet history
    │   │   └── How to claim winnings
    │   │
    │   ├── Token Management
    │   │   ├── Using native tokens
    │   │   ├── Using ERC20 tokens
    │   │   └── Managing approvals
    │   │
    │   ├── Advanced Features
    │   │   ├── Getting player freebets
    │   │   ├── Multi-chain setup
    │   │   └── Error handling
    │   │
    │   └── For Affiliates
    │       ├── Setting up affiliate
    │       └── Tracking commissions
    │
    ├── 📦 SDK Reference [🟢 PRIORITY 3]
    │   ├── React SDK
    │   │   ├── Hooks
    │   │   │   ├── usePlaceBet()
    │   │   │   ├── useBetHistory()
    │   │   │   └── ... (auto-generate?)
    │   │   │
    │   │   └── Contexts
    │   │       ├── BetSwirlProvider
    │   │       └── ... (auto-generate?)
    │   │
    │   ├── Core SDK
    │   │   ├── Client Methods (100+ functions)
    │   │   ├── Wallet Methods
    │   │   ├── Types & Interfaces
    │   │   └── [🤖 AUTO-GENERATE with TypeDoc]
    │   │
    │   └── Wagmi Provider
    │       └── [🤖 AUTO-GENERATE]
    │
    ├── 🏗️ Examples [🟢 PRIORITY 3]
    │   ├── Full Applications
    │   │   ├── MiniApp Template
    │   │   ├── React Dashboard
    │   │   └── Telegram Bot
    │   │
    │   └── Code Snippets
    │       └── Common Patterns
    │
    └── 🔧 Resources [🟢 PRIORITY 3]
        ├── Architecture
        ├── Smart Contracts
        ├── Troubleshooting
        └── FAQ
```


- 🔴 **PRIORITY 1** - Critical
- 🟡 **PRIORITY 2** - Important for main use cases  
- 🟢 **PRIORITY 3** - Can be done later/auto-generated
- 🤖 **AUTO-GENERATE** - Can be automated (TypeDoc, JSDoc)
