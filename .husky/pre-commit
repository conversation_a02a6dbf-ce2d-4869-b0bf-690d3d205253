#!/bin/sh

echo "Running pre-commit checks..."

echo "Running linter and fixing issues..."
# Get list of staged files before linting
STAGED_FILES=$(git diff --cached --name-only)

# Run linter and fix issues
pnpm lint:fix 

# Re-add only the files that were originally staged
for file in $STAGED_FILES; do
    if [ -f "$file" ]; then
        git add "$file"
    fi
done

echo "Building the project..."
# Build the project
pnpm build

echo "Pre-commit checks completed." 

# Exit with an error if any of the above commands fail
if [ $? -ne 0 ]; then
    echo "Pre-commit checks failed."
    exit 1
fi