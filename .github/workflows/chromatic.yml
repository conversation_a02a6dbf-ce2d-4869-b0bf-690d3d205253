name: "Chromatic"

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  chromatic:
    name: Run Chromatic
    runs-on: ubuntu-latest
    # Run chromatic on:
    # - Pushes to main branch (after merge)
    # - Non-draft pull requests to main
    # - Manual workflow dispatch
    if: github.event_name == 'workflow_dispatch'
    
    # (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
    # (github.event_name == 'pull_request' && github.base_ref == 'main' && github.event.pull_request.draft == false) ||
    defaults:
      run:
        working-directory: ./packages/ui-react

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - run: rm ../../package.json ../../pnpm-lock.yaml

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Enable Corepack
        run: corepack enable

      - name: Install pnpm
        run: corepack use pnpm@latest-10

      - name: Install dependencies
        run: pnpm install

      - name: Run Chromatic
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          workingDir: packages/ui-react
          exitZeroOnChanges: false
          skip: "dependabot/**"
