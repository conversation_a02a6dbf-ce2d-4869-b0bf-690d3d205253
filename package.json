{"private": true, "name": "@betswirl/sdk", "packageManager": "pnpm@10.10.0", "scripts": {"build": "pnpm turbo build", "build:libs": "pnpm turbo --filter \"./packages/**\" build", "build:libs:prod": "cross-env NODE_ENV=production pnpm build:libs", "change:add": "pnpm changeset add", "change:version": "pnpm changeset version && pnpm lint:fix", "change:enable-beta": "pnpm changeset pre enter beta", "change:disable-beta": "pnpm changeset pre exit beta", "change:publish": "pnpm build:libs:prod && pnpm changeset publish --no-git-tag", "lint": "biome check --diagnostic-level=error --max-diagnostics=200", "lint:fix": "biome check --write", "prepare": "husky", "test": "rimraf node_modules pnpm-lock.yaml"}, "type": "module", "workspaces": ["packages/core", "packages/ui-react", "packages/providers/*"], "engines": {"node": ">=20.11.1 <24", "npm": "please-use-pnpm", "pnpm": ">=8", "yarn": "please-use-pnpm"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.28.1", "cross-env": "^7.0.3", "husky": "^9.1.7", "rimraf": "^6.0.1", "turbo": "^2.4.4"}}